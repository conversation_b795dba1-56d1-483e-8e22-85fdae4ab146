import { download, get, post } from '../request'
import {
    IPaymentFromFilter,
    IPaymentFrom,
    ICreatePaymentFromParams,
    IConfirmPaymentParams,
    IRejectPaymentParams,
    IUploadInvoiceParams,
    IPageResponse,
    Result
} from '@haierbusiness-front/common-libs'


export const paymentFromApi = {
    // 收款单详情查询-缴费
    getPaymentDetailss: (id: number): Promise<IPaymentFrom> => {
        return get('/mice-bid/api/mice/plat/receive/payment/getDetails', {
            id
        })
    },
    // 财务确认付款并上传付款凭证-缴费
    confirmPaymentPayments: (params: IConfirmPaymentParams): Promise<Result> => {
        return post('/mice-bid/api/mice/plat/receive/payment/confirm/receivePayment', params)
    },
    // 财务驳回付款-缴费
    rejectPayments: (params: IRejectPaymentParams): Promise<Result> => {
        return post('/mice-bid/api/mice/plat/receive/payment/reject/receivePayment', params)
    },
    getPayMentLists: (params: IPaymentFromFilter): Promise<IPageResponse<IPaymentFrom>> => {
        return get('/mice-bid/api/mice/plat/receive/payment/getPage', params)
    },
}
